# 牡丹江师范学院网站仿制版

这是一个仿制牡丹江师范学院官方网站的项目，使用纯HTML、CSS和JavaScript实现。

## 项目特点

### 🎨 设计特色
- 忠实还原原网站的设计风格和布局
- 采用蓝色主色调，体现学术严谨性
- 响应式设计，支持各种设备尺寸
- 现代化的UI交互效果

### 🚀 功能特性
- **轮播图展示**：自动播放的焦点新闻轮播
- **新闻资讯**：要闻、媒体报道等新闻模块
- **公告通知**：重要通知和公告展示
- **校历功能**：交互式日历组件
- **快速导航**：常用系统快速入口
- **数据统计**：学校关键数据动态展示
- **搜索功能**：网站内容搜索
- **移动端适配**：完整的移动端体验

### 💻 技术实现
- **HTML5**：语义化标签，良好的结构
- **CSS3**：
  - Flexbox和Grid布局
  - CSS动画和过渡效果
  - 响应式媒体查询
  - 自定义滚动条样式
- **JavaScript**：
  - 轮播图控制
  - 数字动画效果
  - 滚动监听
  - 触摸手势支持
  - 键盘导航

## 文件结构

```
├── index.html              # 主页面
├── css/
│   ├── style.css          # 主样式文件
│   └── responsive.css     # 响应式样式
├── js/
│   └── script.js          # JavaScript功能
├── images/
│   ├── logo.svg           # 网站Logo
│   ├── banner*.svg        # 轮播图片
│   ├── news*.svg          # 新闻图片
│   ├── media*.svg         # 媒体报道图片
│   ├── nav-*.svg          # 导航图标
│   └── footer-logo.svg    # 底部Logo
└── README.md              # 项目说明
```

## 运行方法

### 方法一：直接打开
直接用浏览器打开 `index.html` 文件

### 方法二：本地服务器
```bash
# 使用Python启动本地服务器
python -m http.server 8080

# 或使用Node.js
npx http-server -p 8080

# 然后访问 http://localhost:8080
```

## 主要功能模块

### 1. 头部导航
- 顶部快捷链接（邮箱、内网、图书馆等）
- 主导航菜单（校情总览、机构设置等）
- 下拉子菜单
- 搜索功能

### 2. 轮播图区域
- 自动播放（5秒间隔）
- 手动控制按钮
- 指示点导航
- 触摸滑动支持（移动端）

### 3. 内容区域
- **左侧**：要闻、媒体牡师
- **右侧**：公告、校历、快速导航

### 4. 数据统计
- 学校关键数据展示
- 数字动画效果
- 滚动触发动画

### 5. 底部信息
- 联系方式
- 版权信息
- 返回顶部按钮

## 响应式断点

- **桌面端**：> 1024px
- **平板端**：768px - 1024px
- **手机端**：< 768px
- **小屏手机**：< 480px

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### CSS架构
- 采用BEM命名规范
- 模块化样式组织
- CSS变量统一管理颜色

### JavaScript特性
- ES6+语法
- 模块化函数设计
- 事件委托优化性能
- 防抖节流处理

### 性能优化
- 图片懒加载
- CSS和JS压缩
- 减少重排重绘
- 合理使用缓存

## 后续改进

- [ ] 添加更多交互动画
- [ ] 集成真实数据API
- [ ] 添加暗色主题
- [ ] 优化SEO
- [ ] 添加PWA支持

## 许可证

本项目仅用于学习和演示目的，请勿用于商业用途。

---

**注意**：本项目是对牡丹江师范学院官方网站的仿制，仅用于技术学习交流，不代表官方立场。
