<!DOCTYPE html>
<html>
<head>
    <title>创建占位图片</title>
</head>
<body>
    <canvas id="canvas" width="800" height="400"></canvas>
    
    <script>
        // 创建轮播图占位图片
        function createBannerImage(text, color, filename) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置背景色
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, 800, 400);
            
            // 设置文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 400, 200);
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            });
        }
        
        // 创建新闻图片
        function createNewsImage(text, filename) {
            const canvas = document.getElementById('canvas');
            canvas.width = 300;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // 设置背景色
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 300, 200);
            
            // 设置文字
            ctx.fillStyle = '#666';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 150, 100);
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            });
        }
        
        // 创建图标
        function createIcon(text, filename) {
            const canvas = document.getElementById('canvas');
            canvas.width = 64;
            canvas.height = 64;
            const ctx = canvas.getContext('2d');
            
            // 设置背景色
            ctx.fillStyle = '#2c5aa0';
            ctx.fillRect(0, 0, 64, 64);
            
            // 设置文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 32, 32);
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            });
        }
        
        // 延迟执行，避免下载冲突
        setTimeout(() => createBannerImage('党代会胜利闭幕', '#2c5aa0', 'banner1.jpg'), 100);
        setTimeout(() => createBannerImage('学术论坛', '#1e4a8c', 'banner2.jpg'), 200);
        setTimeout(() => createBannerImage('学生竞赛', '#3d6bb3', 'banner3.jpg'), 300);
        
        setTimeout(() => {
            createNewsImage('新闻图片', 'news1.jpg');
        }, 400);
        
        setTimeout(() => {
            createNewsImage('媒体报道', 'media1.jpg');
        }, 500);
        
        setTimeout(() => {
            createNewsImage('媒体报道', 'media2.jpg');
        }, 600);
        
        setTimeout(() => {
            createIcon('OA', 'nav-oa.png');
        }, 700);
        
        setTimeout(() => {
            createIcon('图书', 'nav-library.png');
        }, 800);
        
        setTimeout(() => {
            createIcon('教务', 'nav-edu.png');
        }, 900);
        
        setTimeout(() => {
            createIcon('就业', 'nav-job.png');
        }, 1000);
    </script>
</body>
</html>
