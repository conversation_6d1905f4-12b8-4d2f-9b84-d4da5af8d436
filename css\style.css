/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部快捷链接 */
.top-bar {
    background-color: #2c5aa0;
    color: white;
    padding: 8px 0;
    font-size: 12px;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-links a {
    color: white;
    text-decoration: none;
    margin-right: 15px;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.top-links a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.search-box {
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 5px 10px;
    border: none;
    border-radius: 3px 0 0 3px;
    outline: none;
    width: 200px;
}

.search-box button {
    padding: 5px 15px;
    border: none;
    background-color: #1e4a8c;
    color: white;
    border-radius: 0 3px 3px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-box button:hover {
    background-color: #163a6f;
}

/* 头部导航 */
.header {
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
}

.logo img {
    height: 60px;
    width: auto;
}

.main-nav ul {
    list-style: none;
    display: flex;
    align-items: center;
}

.main-nav > ul > li {
    position: relative;
    margin: 0 5px;
}

.main-nav a {
    text-decoration: none;
    color: #333;
    padding: 15px 20px;
    display: block;
    font-weight: 500;
    transition: all 0.3s;
    border-radius: 5px;
}

.main-nav > ul > li > a:hover,
.main-nav > ul > li > a.active {
    background-color: #2c5aa0;
    color: white;
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s;
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu a {
    padding: 12px 20px;
    color: #666;
    font-weight: normal;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
    color: #2c5aa0;
}

/* 轮播图区域 */
.banner-section {
    position: relative;
    height: 400px;
    overflow: hidden;
    margin-bottom: 30px;
}

.banner-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 40px;
}

.slide-content h2 {
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: bold;
}

.slide-content p {
    font-size: 16px;
    opacity: 0.9;
}

/* 轮播图控制按钮 */
.banner-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.banner-controls button {
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s;
    pointer-events: all;
}

.banner-controls button:hover {
    background-color: white;
    transform: scale(1.1);
}

/* 轮播图指示点 */
.banner-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s;
}

.dot.active {
    background-color: white;
    transform: scale(1.2);
}

/* 主要内容区域 */
.main-content {
    background-color: white;
    min-height: 600px;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    padding: 30px 0;
}

/* 通用区块样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 3px solid #2c5aa0;
}

.section-header h2 {
    font-size: 24px;
    color: #2c5aa0;
    font-weight: bold;
}

.more-link {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.more-link:hover {
    color: #2c5aa0;
}

/* 新闻模块 */
.news-section {
    margin-bottom: 40px;
}

.news-item.featured {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.news-item.featured img {
    width: 200px;
    height: 120px;
    object-fit: cover;
    border-radius: 5px;
}

.news-content time {
    color: #666;
    font-size: 12px;
    display: block;
    margin-bottom: 8px;
}

.news-content h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
    line-height: 1.4;
}

.news-content p {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
}

.news-brief {
    list-style: none;
}

.news-brief li {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 15px;
}

.news-brief li:last-child {
    border-bottom: none;
}

.news-brief time {
    color: #999;
    font-size: 12px;
    white-space: nowrap;
}

.news-brief a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
    flex: 1;
}

.news-brief a:hover {
    color: #2c5aa0;
}

/* 媒体牡师模块 */
.media-section {
    margin-bottom: 40px;
}

.media-list {
    display: grid;
    gap: 20px;
}

.media-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.3s;
}

.media-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.media-item img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
}

.media-item h3 {
    font-size: 14px;
    color: #333;
    line-height: 1.4;
}

/* 右侧内容区域 */
.right-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 公告模块 */
.notice-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.notice-list {
    list-style: none;
}

.notice-list li {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.notice-list li:last-child {
    border-bottom: none;
}

.notice-list time {
    color: #999;
    font-size: 12px;
    display: block;
    margin-bottom: 5px;
}

.notice-list a {
    color: #333;
    text-decoration: none;
    font-size: 14px;
    line-height: 1.4;
    transition: color 0.3s;
}

.notice-list a:hover {
    color: #2c5aa0;
}

/* 校历模块 */
.calendar-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calendar-header button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #2c5aa0;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.calendar-header button:hover {
    background-color: #e9ecef;
}

.current-month {
    font-weight: bold;
    color: #2c5aa0;
}

/* 快速导航 */
.quick-nav {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.nav-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
}

.nav-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    color: #2c5aa0;
}

.nav-item img {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
}

.nav-item span {
    font-size: 12px;
    text-align: center;
}

/* 底部 */
.footer {
    background-color: #2c5aa0;
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer-content {
    display: flex;
    gap: 40px;
    align-items: flex-start;
}

.footer-logo img {
    height: 80px;
    width: auto;
}

.footer-info {
    flex: 1;
}

.contact-info p {
    margin-bottom: 8px;
    font-size: 14px;
}

.copyright {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

.copyright a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.copyright a:hover {
    color: white;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: #2c5aa0;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: #1e4a8c;
    transform: translateY(-2px);
}

.back-to-top span {
    font-size: 12px;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}
