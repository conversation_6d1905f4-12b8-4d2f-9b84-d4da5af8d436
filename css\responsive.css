/* 响应式设计 */

/* 平板设备 */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .right-content {
        order: -1;
    }
    
    .main-nav > ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-nav > ul > li {
        margin: 5px;
    }
    
    .banner-section {
        height: 300px;
    }
    
    .slide-content h2 {
        font-size: 24px;
    }
    
    .slide-content p {
        font-size: 14px;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    .top-bar .container {
        flex-direction: column;
        gap: 10px;
    }
    
    .top-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }
    
    .top-links a {
        margin-right: 0;
        font-size: 11px;
        padding: 3px 8px;
    }
    
    .search-box {
        width: 100%;
        justify-content: center;
    }
    
    .search-box input {
        width: 150px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .logo img {
        height: 50px;
    }
    
    .main-nav > ul {
        flex-direction: column;
        width: 100%;
    }
    
    .main-nav > ul > li {
        width: 100%;
        margin: 2px 0;
    }
    
    .main-nav a {
        padding: 12px 15px;
        text-align: center;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background-color: #f8f9fa;
        margin-top: 5px;
        border-radius: 5px;
        display: none;
    }
    
    .dropdown:hover .dropdown-menu {
        display: block;
    }
    
    .banner-section {
        height: 250px;
    }
    
    .slide-content {
        padding: 20px;
    }
    
    .slide-content h2 {
        font-size: 18px;
        margin-bottom: 5px;
    }
    
    .slide-content p {
        font-size: 12px;
    }
    
    .banner-controls {
        padding: 0 10px;
    }
    
    .banner-controls button {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .content-wrapper {
        padding: 20px 0;
    }
    
    .news-item.featured {
        flex-direction: column;
        gap: 15px;
    }
    
    .news-item.featured img {
        width: 100%;
        height: 150px;
    }
    
    .news-content h3 {
        font-size: 16px;
    }
    
    .media-item {
        flex-direction: column;
        text-align: center;
    }
    
    .media-item img {
        width: 100%;
        height: 120px;
    }
    
    .nav-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
    
    .back-to-top span {
        font-size: 10px;
    }
}

/* 小屏手机 */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .top-bar {
        padding: 5px 0;
    }
    
    .top-links a {
        font-size: 10px;
        padding: 2px 6px;
    }
    
    .search-box input {
        width: 120px;
        padding: 4px 8px;
        font-size: 12px;
    }
    
    .search-box button {
        padding: 4px 10px;
        font-size: 12px;
    }
    
    .header .container {
        padding: 10px;
    }
    
    .logo img {
        height: 40px;
    }
    
    .main-nav a {
        padding: 10px;
        font-size: 14px;
    }
    
    .banner-section {
        height: 200px;
    }
    
    .slide-content {
        padding: 15px;
    }
    
    .slide-content h2 {
        font-size: 16px;
    }
    
    .slide-content p {
        font-size: 11px;
    }
    
    .banner-controls button {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .dot {
        width: 10px;
        height: 10px;
    }
    
    .section-header h2 {
        font-size: 20px;
    }
    
    .news-content h3 {
        font-size: 15px;
    }
    
    .news-content p {
        font-size: 13px;
    }
    
    .news-brief a {
        font-size: 13px;
    }
    
    .notice-list a {
        font-size: 13px;
    }
    
    .nav-item {
        padding: 12px;
    }
    
    .nav-item img {
        width: 35px;
        height: 35px;
    }
    
    .nav-item span {
        font-size: 11px;
    }
    
    .footer {
        padding: 30px 0 15px;
    }
    
    .contact-info p {
        font-size: 13px;
    }
    
    .copyright {
        font-size: 11px;
    }
}

/* 超宽屏幕 */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .banner-section {
        height: 500px;
    }
    
    .slide-content h2 {
        font-size: 32px;
    }
    
    .slide-content p {
        font-size: 18px;
    }
    
    .content-wrapper {
        gap: 40px;
    }
}

/* 打印样式 */
@media print {
    .top-bar,
    .banner-controls,
    .banner-dots,
    .back-to-top {
        display: none;
    }
    
    .header {
        position: static;
        box-shadow: none;
    }
    
    .main-nav {
        display: none;
    }
    
    .banner-section {
        height: auto;
    }
    
    .slide {
        position: static;
        opacity: 1;
    }
    
    .slide img {
        max-height: 200px;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .footer {
        margin-top: 20px;
        padding: 20px 0;
    }
    
    a {
        color: #333 !important;
        text-decoration: underline;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .top-bar {
        background-color: #000;
    }
    
    .header {
        border-bottom: 2px solid #000;
    }
    
    .main-nav > ul > li > a:hover,
    .main-nav > ul > li > a.active {
        background-color: #000;
    }
    
    .section-header {
        border-bottom-color: #000;
    }
    
    .section-header h2 {
        color: #000;
    }
    
    .footer {
        background-color: #000;
    }
    
    .back-to-top {
        background-color: #000;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .slide {
        transition: none;
    }
    
    .banner-controls button,
    .nav-item,
    .media-item {
        transition: none;
    }
}
